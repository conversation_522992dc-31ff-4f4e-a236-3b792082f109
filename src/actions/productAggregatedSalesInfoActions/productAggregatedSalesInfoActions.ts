import { getObjectKeys } from "@develop/fe-library/dist/utils"

import api from "api"

import { withActionPrefix } from "actions/utils"

import { customerIdSelector } from "selectors/mainStateSelectors"
import { productAggregatedSalesInfoSelector } from "selectors/productAggregatedSalesInfoSelectors/productAggregatedSalesInfoSelectors"

import { buildProductAggregatedSalesInfoTableSpecialParams } from "utils/buildProductAggregatedSalesInfoTableSpecialParams"
import generateActions from "utils/generateActions"
import { normalizeProductAggregatedSalesInfo } from "utils/normalizeProductAggregatedSalesInfo"
import { normalizeProductAggregatedSalesInfoGrouped } from "utils/normalizeProductAggregatedSalesInfoGrouped"
import {
  buildTableSettingsActionDefinitions,
  buildTableSettingsActionTypes,
} from "utils/tableSettingsHelpers"

import { ASYNC_STATUSES } from "constants/async"
import { EXPANDED_ROW_PAGE_SIZE } from "constants/expandedRowPageSize"
import { PRODUCTS_AGGREGATED_SALES_INFO_TABLE_SETTINGS_KEY } from "constants/productAggregatedSalesInfo"

import type { GridGeneral, ProductAggregatedSalesInfo } from "types"
import type { ProductAggregatedSalesInfoNormalized } from "types/store/ProductAggregatedSalesInfo"
import type { RootState } from "types/store/store"

import { GetProductAggregatedSalesInfoTableDataParams } from "./ProductAggregatedSalesInfoActionsTypes"
import type {
  ProductAggregatedSalesInfoDefinitions,
  ProductAggregatedSalesInfoTypes,
} from "./ProductAggregatedSalesInfoActionsTypes"

const {
  productAggregatedSalesInfoService: {
    getProductAggregatedSalesInfoGrouped,
    getProductAggregatedSalesInfo,
  },
} = api

export const PREFIX = "productAggregatedSalesInfo"

const createActionType = withActionPrefix(PREFIX)

// Use of ActionsTypes causes a strange error when not including sync types.
// TODO: check and fix.
// @ts-expect-error
export const types: ProductAggregatedSalesInfoTypes = {
  getProductAggregatedSalesInfoTableData: createActionType(
    "getProductAggregatedSalesInfoTableData",
    true,
  ),
  changeExpandedRowStates: createActionType("changeExpandedRowStates"),
  expandRow: createActionType("expandRow", true),
  collapseRow: createActionType("collapseRow"),
  ...buildTableSettingsActionTypes(PREFIX),
}

const definitions: ProductAggregatedSalesInfoDefinitions = {
  getProductAggregatedSalesInfoTableData: (
    { params = {}, successCallback = () => {}, failureCallback = () => {} },
    dispatch,
    getState,
  ) => ({
    callApi: async (): Promise<{
      data: GridGeneral<ProductAggregatedSalesInfoNormalized[]>
    }> => {
      const state = getState?.() || {}
      const customerId = customerIdSelector(state)

      const {
        expandedRows = {},
        requestParams,
        groupBy,
      } = params as GetProductAggregatedSalesInfoTableDataParams

      // If group_by is not present, fetch non-grouped data
      if (!groupBy) {
        const response = await getProductAggregatedSalesInfo({
          params: requestParams,
          customerId,
        })

        const { currentPage, pageCount, pageSize, totalCount } =
          response?.data || {}

        const data = response?.data?.data || []

        const payload: GridGeneral<
          Array<ProductAggregatedSalesInfoNormalized>
        > = {
          currentPage,
          pageCount,
          pageSize,
          totalCount,
          data: normalizeProductAggregatedSalesInfo(data),
        }

        return {
          data: payload,
        }
      }

      // Fetch grouped data
      const response = await getProductAggregatedSalesInfoGrouped({
        params: {
          ...requestParams,
          group_by: groupBy,
        },
        customerId,
      })

      const { currentPage, pageCount, pageSize, totalCount } =
        response?.data || {}

      const data = response?.data?.data || []

      // Normalize grouped data to common format
      const dataNormalized = normalizeProductAggregatedSalesInfoGrouped(data)

      const itemsToExpand: Array<ProductAggregatedSalesInfoNormalized> =
        getObjectKeys(expandedRows).reduce((acc, id) => {
          const item = dataNormalized.find((item) => item.id === id)

          if (!item) {
            return acc
          }

          acc.push(item)

          return acc
        }, [] as Array<ProductAggregatedSalesInfoNormalized>)

      // Fetch expanded rows data
      const expandedRowsData = await Promise.all(
        itemsToExpand.map(async (item) => {
          const specialParams = item.group_value
            ? buildProductAggregatedSalesInfoTableSpecialParams({
                groupBy,
                groupValue: item.group_value,
                requestParams,
              })
            : {}

          dispatch?.({
            type: types.changeExpandedRowStates,
            payload: {
              ...definitions.changeExpandedRowStates(
                {
                  id: item.id,
                  status: ASYNC_STATUSES.PENDING,
                  error: null,
                },
                dispatch,
                getState,
              ),
            },
          })

          const expandedRowItemResponse = await getProductAggregatedSalesInfo({
            params: {
              ...requestParams,
              ...specialParams,
              page: 1,
              pageSize: expandedRows[item.id],
            },
            customerId,
          })

          const expandedRowItemResponseData =
            expandedRowItemResponse?.data?.data || []

          dispatch?.({
            type: types.changeExpandedRowStates,
            payload: {
              ...definitions.changeExpandedRowStates(
                {
                  id: item.id,
                  status: ASYNC_STATUSES.FULFILLED,
                  error: null,
                  loadedCount: expandedRowItemResponseData.length,
                },
                dispatch,
                getState,
              ),
            },
          })

          return {
            id: item.id,
            data: expandedRowItemResponseData,
          }
        }),
      )

      console.log({ expandedRows, expandedRowsData })
      // Embed expanded rows data to grouped data
      const dataWithExpandedRows = dataNormalized.map((item) => {
        const expandedRow = expandedRowsData.find(
          ({ id: expandedId }) => item.id === expandedId,
        )

        return {
          ...item,
          children: [
            ...(item.children || []),
            ...normalizeProductAggregatedSalesInfo({
              data: expandedRow?.data || [],
              group_by: item.group_by,
              group_value: item.group_value,
              group_items_count: item.group_items_count,
            }),
          ],
        }
      })

      const payload: GridGeneral<Array<ProductAggregatedSalesInfoNormalized>> =
        {
          currentPage,
          pageCount,
          pageSize,
          totalCount,
          data: dataWithExpandedRows,
        }

      return {
        data: payload,
      }
    },
    successCallback,
    failureCallback,
  }),
  changeExpandedRowStates: (params, dispatch, getState) => {
    const state = (getState?.() || {}) as RootState

    const productAggregatedSalesInfo = productAggregatedSalesInfoSelector(state)

    const expandedRows =
      productAggregatedSalesInfo?.productAggregatedSalesInfoGrouped
        ?.expandedRows || {}

    const loadedCountOld: number = expandedRows[params.id]?.loadedCount || 0

    const loadedCountNew: number = loadedCountOld + (params.loadedCount || 0)

    return {
      ...expandedRows,
      [params.id]: {
        status: params.status,
        error: params.error,
        loadedCount: loadedCountNew,
      },
    }
  },
  clearExpandedRowState: (id, dispatch, getState) => {
    const state = (getState?.() || {}) as RootState

    const productAggregatedSalesInfo = productAggregatedSalesInfoSelector(state)

    const expandedRows =
      productAggregatedSalesInfo?.productAggregatedSalesInfoGrouped
        ?.expandedRows || {}

    const { [id]: removedRow, ...rest } = expandedRows

    return rest
  },
  expandRow: (
    { params = {}, successCallback = () => {}, failureCallback = () => {} },
    dispatch,
    getState,
  ) => ({
    callApi: async (): Promise<{
      data: GridGeneral<ProductAggregatedSalesInfoNormalized[]>
    }> => {
      const state = (getState?.() || {}) as RootState

      const customerId = customerIdSelector(state)
      const productAggregatedSalesInfo =
        productAggregatedSalesInfoSelector(state)

      const productAggregatedSalesInfoGroupedData: Array<ProductAggregatedSalesInfoNormalized> =
        productAggregatedSalesInfo?.productAggregatedSalesInfoGrouped?.data
          ?.data || []

      const { id: expandedItemId, page, requestParams = {} } = params

      dispatch?.({
        type: types.changeExpandedRowStates,
        payload: {
          ...definitions.changeExpandedRowStates(
            {
              id: String(expandedItemId),
              status: ASYNC_STATUSES.PENDING,
              error: null,
            },
            dispatch,
            getState,
          ),
        },
      })

      const response = await getProductAggregatedSalesInfo({
        params: {
          ...requestParams,
          page,
          pageSize: EXPANDED_ROW_PAGE_SIZE,
        },
        customerId,
      })

      const data: Array<ProductAggregatedSalesInfo> = response?.data?.data || []

      dispatch?.({
        type: types.changeExpandedRowStates,
        payload: {
          id: expandedItemId,
          status: ASYNC_STATUSES.FULFILLED,
          error: null,
          loadedCount: data.length,
        },
      })

      const dataWithExpandedRow: Array<ProductAggregatedSalesInfoNormalized> =
        productAggregatedSalesInfoGroupedData.map((item) => {
          if (item.id === expandedItemId) {
            return {
              ...item,
              children: [
                ...(item.children || []),
                ...normalizeProductAggregatedSalesInfo({
                  data,
                  group_by: item.group_by,
                  group_value: item.group_value,
                  group_items_count: item.group_items_count,
                }),
              ],
            }
          }

          return item
        })

      const payload: GridGeneral<Array<ProductAggregatedSalesInfoNormalized>> =
        {
          ...productAggregatedSalesInfo.productAggregatedSalesInfoGrouped.data,
          data: dataWithExpandedRow,
        }

      return { data: payload }
    },
    successCallback,
    failureCallback,
  }),
  collapseRow: (id, dispatch, getState) => {
    const state = (getState?.() || {}) as RootState

    const productAggregatedSalesInfo = productAggregatedSalesInfoSelector(state)

    const data: Array<ProductAggregatedSalesInfoNormalized> =
      productAggregatedSalesInfo?.productAggregatedSalesInfoGrouped?.data
        ?.data || []

    return data.map((item): ProductAggregatedSalesInfoNormalized => {
      if (item.id === id) {
        const { children, ...rest } = item

        // Clear expanded row status
        dispatch?.({
          type: types.changeExpandedRowStates,
          payload: {
            ...definitions.changeExpandedRowStates(
              {
                id: String(id),
                status: ASYNC_STATUSES.IDLE,
                error: null,
              },
              dispatch,
              getState,
            ),
          },
        })

        return rest
      }

      return item
    })
  },
  ...buildTableSettingsActionDefinitions({
    storeKey: PREFIX,
    tableSettingsKey: PRODUCTS_AGGREGATED_SALES_INFO_TABLE_SETTINGS_KEY,
  }),
}

const productAggregatedSalesInfoActions = generateActions(
  definitions,
  types,
) as ProductAggregatedSalesInfoDefinitions

export { productAggregatedSalesInfoActions }
