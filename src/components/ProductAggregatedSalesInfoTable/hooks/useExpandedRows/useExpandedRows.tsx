import { useMemo } from "react"
import { getObjectKeys } from "@develop/fe-library/dist/utils"

import { getItem } from "utils/storage"

import { SESSION_STORAGE_KEYS } from "constants/sessionStorage"

import type { UseExpandedRows } from "./UseExpandedRowsTypes"

export const useExpandedRows: UseExpandedRows = () => {
  return useMemo((): Set<string> => {
    const expandedRowIdsFromSessionStorage = getItem(
      SESSION_STORAGE_KEYS.DASHBOARD_PRODUCTS_EXPANDED_ROWS,
      true,
    )

    if (!expandedRowIdsFromSessionStorage) {
      return new Set<string>()
    }

    const parsed: Record<string, number> = JSON.parse(
      expandedRowIdsFromSessionStorage,
    )

    return new Set<string>(getObjectKeys(parsed))
  }, [])
}
