import React from "react"
import { Box, Ellipsis } from "@develop/fe-library"

import l from "utils/intl"

import { NOT_AVAILABLE } from "constants/common"

import type { ProductAggregatedSalesInfoTableInteractiveEntryParams } from "../../ProductAggregatedSalesInfoTableTypes"

export const TitleCell = ({
  value,
}: ProductAggregatedSalesInfoTableInteractiveEntryParams) => {
  if (!value) {
    return <>{l(NOT_AVAILABLE)}</>
  }

  return (
    <Box width="100%">
      <Ellipsis
        rows={3}
        width="100%"
        typographyProps={{
          variant: "--font-body-text-9",
          textAlign: "left",
        }}
      >
        {value}
      </Ellipsis>
    </Box>
  )
}
