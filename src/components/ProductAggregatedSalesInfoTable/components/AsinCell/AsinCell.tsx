import React from "react"
import { TextLink } from "@develop/fe-library"

import l from "utils/intl"
import { getAmazonProductLink } from "utils/links"

import { NOT_AVAILABLE } from "constants/common"

import { getAsinFormatted } from "../../utils"

import { AsinCellProps } from "./AsinCellTypes"

export const AsinCell = ({
  tableEntryParams,
  amazonMarketplaces,
}: AsinCellProps) => {
  const { item } = tableEntryParams
  const { product_asin, marketplace_id } = item

  const marketplace = amazonMarketplaces?.find(
    ({ id }) => id === marketplace_id,
  )

  if (!marketplace) {
    return l(NOT_AVAILABLE)
  }

  const { sales_channel } = marketplace

  const link = getAmazonProductLink(sales_channel, product_asin)

  return (
    <TextLink
      href={link}
      rel="noopener noreferrer"
      target="_blank"
      typographyProps={{
        variant: "--font-body-text-9",
      }}
    >
      {getAsinFormatted(tableEntryParams)}
    </TextLink>
  )
}
