import type { TableRowParams } from "@develop/fe-library"

import type { ProductAggregatedSalesInfoTableData } from "components/ProductAggregatedSalesInfoTable/ProductAggregatedSalesInfoTableTypes"

export const actionColumnRowSpan = ({
  item,
}: TableRowParams<ProductAggregatedSalesInfoTableData>): number => {
  const { tableRowMetaData = {}, group_items_count } = item

  const {
    isExpandedRowChild = false,
    expandedRowIndex,
    expandedParentRowChildrenCount = 0,
  } = tableRowMetaData

  if (!isExpandedRowChild) {
    return 1
  }

  if (expandedRowIndex === 0) {
    const groupItemsCount = group_items_count
      ? parseInt(group_items_count, 10)
      : 0

    const hasRemainingItemsToLoad =
      groupItemsCount > expandedParentRowChildrenCount

    console.log({
      hasRemainingItemsToLoad,
      group_items_count,
      groupItemsCount,
      expandedParentRowChildrenCount,
    })

    if (hasRemainingItemsToLoad) {
      return expandedParentRowChildrenCount + 1
    }

    return expandedParentRowChildrenCount
  }

  return 0
}
