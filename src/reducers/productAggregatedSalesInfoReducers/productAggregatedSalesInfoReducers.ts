import { API_STATUS } from "api/constants"

import {
  productAggregatedSalesInfoActionsPrefix,
  types as ActionTypes,
} from "actions/productAggregatedSalesInfoActions"

import type { Reduction } from "reducers/types"

import generateReducer from "utils/generateReducer"
import { buildTableSettingsReductions } from "utils/tableSettingsHelpers"

import { ASYNC_STATUSES } from "constants/async"

import { productsAggregatesSalesInfoInitialState } from "./initialState"

import type { GridGeneral } from "types"
import type { ProductAggregatedSalesInfoState } from "types/store/ProductAggregatedSalesInfo"
import { ProductAggregatedSalesInfoNormalized } from "types/store/ProductAggregatedSalesInfo"

const {
  getProductAggregatedSalesInfoTableData,
  changeExpandedRowStates,
  clearExpandedRowState,
  expandRow,
  collapseRow,
} = ActionTypes

const { FAILURE, REQUEST, SUCCESS } = API_STATUS

const reductions: Reduction<ProductAggregatedSalesInfoState> = {
  [getProductAggregatedSalesInfoTableData[REQUEST]]: (
    state,
  ): ProductAggregatedSalesInfoState => {
    return {
      ...state,
      productAggregatedSalesInfoGrouped: {
        ...state.productAggregatedSalesInfoGrouped,
        status: ASYNC_STATUSES.PENDING,
      },
    }
  },
  [getProductAggregatedSalesInfoTableData[SUCCESS]]: (
    state,
    payload: GridGeneral<ProductAggregatedSalesInfoNormalized[]>,
  ): ProductAggregatedSalesInfoState => {
    return {
      ...state,
      productAggregatedSalesInfoGrouped: {
        ...state.productAggregatedSalesInfoGrouped,
        error: null,
        status: ASYNC_STATUSES.FULFILLED,
        data: payload,
      },
    }
  },
  [getProductAggregatedSalesInfoTableData[FAILURE]]: (
    state,
    payload,
  ): ProductAggregatedSalesInfoState => {
    return {
      ...state,
      productAggregatedSalesInfoGrouped: {
        ...state.productAggregatedSalesInfoGrouped,
        status: ASYNC_STATUSES.REJECTED,
        error: payload,
      },
    }
  },
  [changeExpandedRowStates]: (
    state,
    payload,
  ): ProductAggregatedSalesInfoState => {
    return {
      ...state,
      productAggregatedSalesInfoGrouped: {
        ...state.productAggregatedSalesInfoGrouped,
        expandedRows: payload,
      },
    }
  },
  [clearExpandedRowState]: (
    state,
    payload,
  ): ProductAggregatedSalesInfoState => {
    return {
      ...state,
      productAggregatedSalesInfoGrouped: {
        ...state.productAggregatedSalesInfoGrouped,
        expandedRows: payload,
      },
    }
  },
  [expandRow[SUCCESS]]: (
    state,
    payload: GridGeneral<ProductAggregatedSalesInfoNormalized[]>,
  ): ProductAggregatedSalesInfoState => {
    return {
      ...state,
      productAggregatedSalesInfoGrouped: {
        ...state.productAggregatedSalesInfoGrouped,
        data: payload,
      },
    }
  },
  [collapseRow]: (
    state,
    payload: Array<ProductAggregatedSalesInfoNormalized>,
  ): ProductAggregatedSalesInfoState => {
    return {
      ...state,
      productAggregatedSalesInfoGrouped: {
        ...state.productAggregatedSalesInfoGrouped,
        data: {
          ...state.productAggregatedSalesInfoGrouped.data,
          data: payload,
        },
      },
    }
  },
  ...buildTableSettingsReductions<ProductAggregatedSalesInfoState>(
    productAggregatedSalesInfoActionsPrefix,
  ),
}

const reducer = generateReducer(
  productsAggregatesSalesInfoInitialState,
  reductions,
)

export default reducer
