import type {
  AsyncStatus,
  GridGeneral,
  ProductAggregatedSalesInfo,
} from "types"
import { ProductAggregatedSalesInfoTableGroupBy } from "types/ProductAggregatedSalesInfoTableGroupBy"
import type { TableSettingsState } from "types/TableSettings"

export type ProductAggregatedSalesInfoNormalized =
  Partial<ProductAggregatedSalesInfo> & {
    id: string
    group_by?: ProductAggregatedSalesInfoTableGroupBy
    group_value?: string
    group_items_count?: string
    total_expenses?: number
    children?: Array<ProductAggregatedSalesInfoNormalized>
  }

export type ExpandedRowState = {
  status: AsyncStatus
  error: any | null
  loadedCount: number
}

export type ProductAggregatedSalesInfoState = {
  productAggregatedSalesInfoGrouped: {
    data: GridGeneral<Array<ProductAggregatedSalesInfoNormalized>>
    status: AsyncStatus
    error: any | null
    expandedRows: Record<string, ExpandedRowState>
  }
} & TableSettingsState
